package com.tucun.ai.admin.module.agent.stream;

import static com.tucun.ai.admin.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.tucun.ai.admin.module.agent.enums.ErrorCodeConstants.CHAT_NOT_SAVE;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

import com.tucun.ai.admin.module.agent.platform.dto.request.LLMBlockingRequest;
import com.tucun.ai.admin.module.agent.platform.dto.response.LLMBlockingResponse;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tucun.ai.admin.framework.common.exception.ServiceException;
import com.tucun.ai.admin.module.agent.controller.app.chat_message.vo.AskRequest;
import com.tucun.ai.admin.module.agent.controller.app.chat_message.vo.MessageSource;
import com.tucun.ai.admin.module.agent.converter.AgentChatConverter;
import com.tucun.ai.admin.module.agent.dal.dataobject.chat.ChatDO;
import com.tucun.ai.admin.module.agent.dal.dataobject.chat_message.ChatMessageDO;
import com.tucun.ai.admin.module.agent.dal.mysql.chat.ChatMapper;
import com.tucun.ai.admin.module.agent.dal.mysql.chat_message.ChatMessageMapper;
import com.tucun.ai.admin.module.agent.service.dailylimit.DailyLimitService;
import com.tucun.ai.admin.module.agent.platform.LLMPlatformService;
import com.tucun.ai.admin.module.agent.platform.dto.request.LLMStreamingRequest;
import com.tucun.ai.admin.module.agent.platform.dto.response.LLMStreamingResponse;
import com.tucun.ai.admin.module.agent.platform.enums.LLMDataType;
import com.tucun.ai.admin.module.agent.stream.config.DifyConfigType;
import com.tucun.ai.admin.module.agent.stream.listener.SSEListener;
import com.tucun.ai.admin.module.agent.stream.response.AgentResponseData;
import com.tucun.ai.admin.module.agent.stream.response.SSEResponseCallback;
import com.tucun.ai.admin.module.agent.stream.response.chat.ChatData;
import com.tucun.ai.admin.module.agent.stream.util.EncodingUtils;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

/**
 * 简化的 Agent 聊天流式响应服务实现
 * 移除复杂的状态管理，采用简单直接的处理方式
 */
@Slf4j
@Service
@Validated
public class AgentChatStreamServiceImpl implements AgentChatStreamService, SSEResponseCallback<DifyConfigType> {

    @Resource
    private LLMPlatformService llmPlatformService;

    @Resource
    private ChatMapper chatMapper;

    @Resource
    private ChatMessageMapper chatMessageMapper;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private PlatformTransactionManager transactionManager;

    @Resource
    private DailyLimitService dailyLimitService;

    @Value("${agent.sse.timeout:60000}")
    private Long sseTimeout;

    @Override
    public SseEmitter askAgent(AskRequest request) {
        final SseEmitter emitter = new SseEmitter(sseTimeout);
        configureEmitterLifecycle(emitter);

        try {
            // 检查每日限制
            dailyLimitService.checkDailyLimit(request.getUserId());
        } catch (ServiceException e) {
            return handleDailyLimitExceeded(emitter, e.getMessage());
        }

        return handleStreamingRequest(request, emitter);
    }

    /**
     * 处理每日限制超出的情况
     */
    private SseEmitter handleDailyLimitExceeded(SseEmitter emitter, String errorMessage) {
        try {
            sendErrorResponse(emitter, errorMessage, "daily-limit-exceeded");
            emitter.complete();
        } catch (Exception e) {
            log.error("发送每日限制错误信息失败: {}", e.getMessage(), e);
            emitter.completeWithError(e);
        }
        return emitter;
    }

    /**
     * 处理流式请求
     */
    private SseEmitter handleStreamingRequest(AskRequest request, SseEmitter emitter) {
        // 用于存储流式响应的关键信息
        AtomicReference<String> finalNodeContent = new AtomicReference<>("");
        AtomicReference<String> taskIdRef = new AtomicReference<>();
        AtomicReference<String> messageIdRef = new AtomicReference<>();
        AtomicReference<String> conversationIdRef = new AtomicReference<>();
        AtomicBoolean isArticleMode = new AtomicBoolean(false);

        try {
            LLMStreamingRequest streamingRequest = buildStreamingRequest(request);
            SSEListener<LLMStreamingResponse> listener = createSseListener(
                request, emitter, finalNodeContent, taskIdRef, messageIdRef, 
                conversationIdRef, isArticleMode
            );

            llmPlatformService.sendStreamingRequest(streamingRequest, listener, DifyConfigType.CHAT_GENERATE);
            
        } catch (Exception e) {
            log.error("处理流式请求时发生错误: {}", e.getMessage(), e);
            handleError(emitter, e, "stream-request-error");
        }

        return emitter;
    }

    /**
     * 处理流式消息
     */
    private void processStreamingMessage(String content, String taskId, String messageId, String conversationId, 
                                       SseEmitter emitter, AtomicBoolean isArticleMode, Integer messageType) {
        try {
            if (messageType != null && messageType == 5) {
                isArticleMode.set(true);
            }
            
            sendStreamingResponse(content, taskId, messageId, conversationId, messageType, emitter);
            
        } catch (Exception e) {
            log.error("处理流式消息失败, TaskId: {}: {}", taskId, e.getMessage(), e);
            handleError(emitter, e, taskId);
        }
    }

    /**
     * 处理最终节点输出
     */
    private void processFinalNodeOutput(String content, String taskId, String messageId, String conversationId, 
                                      SseEmitter emitter, AtomicReference<String> finalNodeContent) {
        try {
            if (content != null && !content.trim().isEmpty()) {
                finalNodeContent.set(content);
                log.debug("设置最终节点内容, TaskId: {}, Content length: {}", taskId, content.length());
            }
        } catch (Exception e) {
            log.error("处理最终节点输出失败, TaskId: {}: {}", taskId, e.getMessage(), e);
            handleError(emitter, e, taskId);
        }
    }

    /**
     * 处理完成事件 - 只在这里完成emitter
     */
    private void processCompletion(AskRequest request, SseEmitter emitter, AtomicReference<String> finalNodeContent,
                                 String taskId, String messageId, String conversationId) {
        try {
            String finalContentStr = finalNodeContent.get();
            if (finalContentStr == null || finalContentStr.trim().isEmpty()) {
                log.warn("最终内容为空，直接完成emitter, TaskId: {}", taskId);
                emitter.complete();
                return;
            }

            String initialTitle = "新对话";
            
            // 异步保存数据并发送结束响应
            saveChatRecordAsync(request, initialTitle, conversationId)
                .thenCompose(chat -> saveChatMessageAsync(chat.getId(), request, finalContentStr, messageId)
                    .thenApply(message -> new Object[]{chat, message}))
                .thenAccept(result -> {
                    ChatDO chat = (ChatDO) ((Object[]) result)[0];
                    generateChatTitleAsync(chat, request.getAskQuestion());
                    
                    try {
                        sendEndResponse(taskId, messageId, conversationId, finalContentStr, chat.getId(), emitter);
                        emitter.complete(); // 只在这里完成emitter
                        
                    } catch (Exception sendEx) {
                        log.error("发送完成响应失败, TaskId: {}: {}", taskId, sendEx.getMessage(), sendEx);
                        emitter.completeWithError(sendEx);
                    }
                })
                .exceptionally(ex -> {
                    log.error("保存聊天记录失败, TaskId: {}: {}", taskId, ex.getMessage(), ex);
                    handleError(emitter, ex, taskId);
                    return null;
                });
                
        } catch (Exception e) {
            log.error("处理完成事件失败, TaskId: {}: {}", taskId, e.getMessage(), e);
            handleError(emitter, e, taskId);
        }
    }

    /**
     * 发送结束响应
     */
    private void sendEndResponse(String taskId, String messageId, String conversationId, String content, 
                               Long chatId, SseEmitter emitter) {
        try {
            AgentResponseData<ChatData> responseData = new AgentResponseData<>(
                ChatData.builder()
                    .message(content)
                    .type("MESSAGE_END")
                    .taskId(taskId)
                    .llmMessageId(messageId)
                    .conversationId(conversationId)
                    .chatId(chatId)
                    .build()
            );

            String jsonString = objectMapper.writeValueAsString(responseData);
            jsonString = EncodingUtils.sanitizeJsonString(jsonString);
            emitter.send(SseEmitter.event().data(jsonString + "\n", MediaType.APPLICATION_JSON));
            
        } catch (Exception e) {
            log.error("发送结束响应失败, TaskId: {}: {}", taskId, e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 发送流式内容响应
     */
    private void sendStreamingResponse(String content, String taskId, String messageId, String conversationId, 
                                     Integer messageType, SseEmitter emitter) {
        try {
            AgentResponseData<ChatData> responseData = new AgentResponseData<>(
                ChatData.builder()
                    .message(content)
                    .type("MESSAGE_CONTENT")
                    .taskId(taskId)
                    .llmMessageId(messageId)
                    .conversationId(conversationId)
                    .build()
            );

            String jsonString = objectMapper.writeValueAsString(responseData);
            jsonString = EncodingUtils.sanitizeJsonString(jsonString);
            emitter.send(SseEmitter.event().data(jsonString + "\n", MediaType.APPLICATION_JSON));
            
        } catch (Exception e) {
            log.error("发送流式响应失败, TaskId: {}: {}", taskId, e.getMessage(), e);
            handleError(emitter, e, taskId);
        }
    }

    /**
     * 配置Emitter的生命周期回调
     */
    private void configureEmitterLifecycle(SseEmitter emitter) {
        emitter.onCompletion(() -> log.debug("Emitter已完成"));
        emitter.onTimeout(() -> log.warn("Emitter超时"));
        emitter.onError(throwable -> log.error("Emitter发生错误: {}", throwable.getMessage(), throwable));
    }

    /**
     * 发送错误响应
     */
    private void sendErrorResponse(SseEmitter emitter, String errorMessage, String taskId) {
        try {
            AgentResponseData<ChatData> responseData = new AgentResponseData<>(
                ChatData.builder()
                    .message(errorMessage)
                    .type("ERROR")
                    .taskId(taskId)
                    .build()
            );

            String jsonString = objectMapper.writeValueAsString(responseData);
            jsonString = EncodingUtils.sanitizeJsonString(jsonString);
            emitter.send(SseEmitter.event().data(jsonString + "\n", MediaType.APPLICATION_JSON));

        } catch (Exception e) {
            log.error("发送ERROR事件响应失败, TaskId: {}: {}", taskId, e.getMessage());
        }
    }

    /**
     * 统一处理错误
     */
    private void handleError(SseEmitter emitter, Throwable throwable, String taskId) {
        try {
            if (throwable instanceof ServiceException se) {
                sendErrorResponse(emitter, se.getMessage(), taskId);
            } else {
                String userFriendlyMessage = buildUserFriendlyErrorMessage(throwable);
                sendErrorResponse(emitter, userFriendlyMessage, taskId);
            }
            emitter.completeWithError(throwable);
        } catch (Exception sendEx) {
            log.error("发送错误响应失败，直接结束emitter, TaskId: {}: {}", taskId, sendEx.getMessage(), sendEx);
            emitter.completeWithError(throwable);
        }
    }

    /**
     * 构建用户友好的错误消息
     */
    private String buildUserFriendlyErrorMessage(Throwable throwable) {
        if (throwable.getMessage() != null && throwable.getMessage().contains("timeout")) {
            return "请求超时，请稍后重试";
        }
        return "系统繁忙，请稍后重试";
    }

    /**
     * 构建流式请求
     */
    private LLMStreamingRequest buildStreamingRequest(AskRequest request) {
        LLMStreamingRequest streamingRequest = new LLMStreamingRequest();
        streamingRequest.setQuery(request.getAskQuestion());
        streamingRequest.setUserId(request.getUserId().toString());
        streamingRequest.setConversationId(request.getConversationId());
        return streamingRequest;
    }

    /**
     * 创建SSE监听器
     */
    private SSEListener<LLMStreamingResponse> createSseListener(
            AskRequest request, SseEmitter emitter, AtomicReference<String> finalNodeContent,
            AtomicReference<String> taskIdRef, AtomicReference<String> messageIdRef,
            AtomicReference<String> conversationIdRef, AtomicBoolean isArticleMode) {

        return new SSEListener<LLMStreamingResponse>() {
            @Override
            public void onDataReceived(LLMStreamingResponse data) {
                String taskId = taskIdRef.get();
                String messageId = messageIdRef.get();
                String conversationId = conversationIdRef.get();

                // 更新引用值
                if (taskId == null && data.getTaskId() != null) {
                    taskId = data.getTaskId();
                    taskIdRef.set(taskId);
                }
                if (messageId == null && data.getMessageId() != null) {
                    messageId = data.getMessageId();
                    messageIdRef.set(messageId);
                }
                if (conversationId == null && data.getConversationId() != null) {
                    conversationId = data.getConversationId();
                    conversationIdRef.set(conversationId);
                }

                processData(data, taskId, messageId, conversationId, emitter, request,
                           finalNodeContent, isArticleMode);
            }

            @Override
            public void onError(Exception e) {
                String taskId = taskIdRef.get() != null ? taskIdRef.get() : "unknown";
                log.error("SSE监听器发生错误, TaskId: {}: {}", taskId, e.getMessage(), e);
                handleError(emitter, e, taskId);
            }

            @Override
            public void onComplete() {
                String taskId = taskIdRef.get() != null ? taskIdRef.get() : "unknown";
                log.debug("SSE监听器完成, TaskId: {}", taskId);
                // 不在这里完成emitter，让数据处理逻辑决定何时完成
            }
        };
    }

    /**
     * 处理流式响应数据
     */
    private void processData(LLMStreamingResponse data, String taskId, String messageId, String conversationId,
                           SseEmitter emitter, AskRequest request, AtomicReference<String> finalNodeContent,
                           AtomicBoolean isArticleMode) {

        LLMDataType dataType = data.getDataType();
        String content = data.getContent();
        Integer messageType = request.getMessageType();

        switch (dataType) {
            case STREAMING_MESSAGE:
                processStreamingMessage(content, taskId, messageId, conversationId, emitter, isArticleMode, messageType);
                break;
            case FINAL_NODE_OUTPUT:
                processFinalNodeOutput(content, taskId, messageId, conversationId, emitter, finalNodeContent);
                break;
            case WORKFLOW_COMPLETE:
            case END:
                processCompletion(request, emitter, finalNodeContent, taskId, messageId, conversationId);
                break;
            case ERROR:
                handleError(emitter, new RuntimeException("LLM Error: " + data.getErrorMessage()), taskId);
                break;
            case NODE_START:
                // 可以根据需要处理节点开始事件
                log.debug("节点开始事件, TaskId: {}", taskId);
                break;
            default:
                log.debug("未处理的数据类型: {}, TaskId: {}", dataType, taskId);
                break;
        }
    }

    /**
     * 在事务中执行操作
     */
    private <T> T executeInTransaction(java.util.function.Supplier<T> action) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            T result = action.get();
            transactionManager.commit(status);
            return result;
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw e;
        }
    }

    /**
     * 构建聊天消息对象
     */
    private ChatMessageDO buildChatMessage(Long chatId, AskRequest request, String answerQuestion, String messageId) {
        ChatMessageDO message = new ChatMessageDO();
        message.setChatId(chatId);
        message.setUserId(request.getUserId());
        message.setAskQuestion(request.getAskQuestion());
        message.setAnswerQuestion(answerQuestion);
        message.setLlmMessageId(messageId);
        message.setMessageType(request.getMessageType());
        return message;
    }

    @Override
    public String formatResponse(String data, DifyConfigType responseType, String taskId) {
        return data;
    }

    @Override
    public boolean stopGeneration(String taskId, DifyConfigType type) {
        log.info("停止生成请求, TaskId: {}, Type: {}", taskId, type);
        return true;
    }

    /**
     * 异步生成聊天标题
     */
    @Async
    protected void generateChatTitleAsync(ChatDO chat, String userQuestion) {
        try {
            String titlePrompt = "请为以下对话生成一个简短的标题（不超过24个字符）：" + userQuestion;

            LLMBlockingRequest titleRequest = new LLMBlockingRequest();
            titleRequest.setQuery(titlePrompt);
            titleRequest.setUserId(chat.getUserId().toString());

            LLMBlockingResponse titleResponse = llmPlatformService.sendBlockingRequest(titleRequest, DifyConfigType.CHAT_GENERATE);

            if (titleResponse != null && titleResponse.getContent() != null) {
                String generatedTitle = titleResponse.getContent().trim();
                if (generatedTitle.length() > 24) {
                    generatedTitle = generatedTitle.substring(0, 24);
                }

                chat.setChatTitle(generatedTitle);
                chatMapper.updateById(chat);
                log.debug("已更新聊天标题: {}", generatedTitle);
            }
        } catch (Exception e) {
            log.warn("生成聊天标题失败，保持默认标题: {}", e.getMessage());
        }
    }

    /**
     * 异步保存聊天记录
     */
    @Async
    protected CompletableFuture<ChatDO> saveChatRecordAsync(AskRequest request, String title, String conversationId) {
        return CompletableFuture.supplyAsync(() -> executeInTransaction(() -> {
            try {
                ChatDO chat = AgentChatConverter.INSTANCE.convertRequest(request);
                chat.setChatTitle(title);
                chat.setConversationId(conversationId);
                chatMapper.insert(chat);
                return chat;
            } catch (Exception e) {
                log.error("保存聊天记录失败: {}", e.getMessage(), e);
                throw exception(CHAT_NOT_SAVE);
            }
        }));
    }

    /**
     * 异步保存聊天消息
     */
    @Async
    protected CompletableFuture<ChatMessageDO> saveChatMessageAsync(Long chatId, AskRequest request, String answer, String messageId) {
        return CompletableFuture.supplyAsync(() -> executeInTransaction(() -> {
            try {
                ChatMessageDO message = buildChatMessage(chatId, request, answer, messageId);
                chatMessageMapper.insert(message);
                return message;
            } catch (Exception e) {
                log.error("保存聊天消息失败: {}", e.getMessage(), e);
                throw exception(CHAT_NOT_SAVE);
            }
        }));
    }
}
